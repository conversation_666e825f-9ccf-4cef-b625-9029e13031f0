import React, { useState } from 'react';
import { FaWallet, FaChartLine, FaTimes, FaInfoCircle, FaShieldAlt } from 'react-icons/fa';
import { Node, SocialProfiles } from '@/services/neo4jService';
import WalletInfoPanel from './WalletInfoPanel';
import WalletAnalysisPanel from './WalletAnalysisPanel';

interface RightSidePanelProps {
  // From WalletInfoPanel
  walletData: {
    id: string;
    address: string;
    balance?: string;
    transactionCount?: number;
    label?: string;
    tags?: string[];
    usdValue?: number;
    percentage?: number;
    riskScore?: number;
    connections?: number;
    // Social media connectivity
    socialProfiles?: SocialProfiles;
    hasVerifiedSocials?: boolean;
    socialScore?: number;
  } | null;

  // From WalletAnalysisPanel
  selectedWalletForAnalysis: Node | null;
  showWalletAnalysis: boolean;
  onCloseAnalysis: () => void;

  // Panel control
  onClose?: () => void;
  onAddToWatchList?: (address: string) => void; // Callback when wallet is added to watch list
}

const RightSidePanel: React.FC<RightSidePanelProps> = ({
  walletData,
  selectedWalletForAnalysis,
  showWalletAnalysis,
  onCloseAnalysis,
  onClose,
  onAddToWatchList
}) => {
  const [activeTab, setActiveTab] = useState<'details' | 'analysis'>('details');

  // Determine which panels should be shown (before any early returns)
  const hasWalletData = !!walletData;
  const hasAnalysisData = showWalletAnalysis && !!selectedWalletForAnalysis;

  // Auto-set active tab based on available data (hook must be before early return)
  React.useEffect(() => {
    if (hasAnalysisData && !hasWalletData) {
      setActiveTab('analysis');
    } else if (hasWalletData && !hasAnalysisData) {
      setActiveTab('details');
    }
  }, [hasWalletData, hasAnalysisData]);

  // If no data at all, show empty state (early return after all hooks)
  if (!hasWalletData && !hasAnalysisData) {
    return (
      <div className="bg-slate-800 rounded-xl border border-slate-600 h-full">
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-blue-500 rounded-full blur-lg opacity-20"></div>
            <div className="relative w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center">
              <FaWallet className="text-white text-2xl" />
            </div>
          </div>
          <h3 className="text-xl font-bold mb-3 text-white">Wallet Information</h3>
          <p className="text-gray-400 text-center leading-relaxed">
            Click on any bubble in the visualization to see detailed wallet information,
            transaction history, and network analysis.
          </p>
          <div className="mt-6 flex items-center gap-2 text-sm text-blue-400">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <span>Ready to explore</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-slate-800 rounded-xl border border-slate-600 h-full flex flex-col">
      {/* Header with tabs */}
      <div className="flex items-center justify-between p-4 border-b border-slate-600">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <FaWallet className="text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-white">
              Wallet Information
            </h2>
            <p className="text-sm text-gray-400">
              {hasWalletData && walletData?.address ?
                `${walletData.address.slice(0, 10)}...${walletData.address.slice(-8)}` :
                hasAnalysisData && selectedWalletForAnalysis?.address ?
                `${selectedWalletForAnalysis.address.slice(0, 10)}...${selectedWalletForAnalysis.address.slice(-8)}` :
                'Select a wallet'
              }
            </p>
          </div>
        </div>

        <button
          onClick={onClose}
          className="p-2 hover:bg-slate-700 rounded-lg transition-colors text-white"
          title="Close panel"
        >
          <FaTimes className="w-4 h-4" />
        </button>
      </div>

      {/* Tab Navigation - only show if both panels have data */}
      {hasWalletData && hasAnalysisData && (
        <div className="flex border-b border-slate-600">
          <button
            onClick={() => setActiveTab('details')}
            className={`flex-1 p-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
              activeTab === 'details'
                ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-500/10'
                : 'text-gray-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            <FaInfoCircle size={14} />
            Address Details
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`flex-1 p-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
              activeTab === 'analysis'
                ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-500/10'
                : 'text-gray-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            <FaShieldAlt size={14} />
            Risk Analysis
          </button>
        </div>
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto scrollable-container bg-slate-800">
        {/* Address Details Tab */}
        {(activeTab === 'details' || !hasAnalysisData) && hasWalletData && (
          <div className="h-full p-4">
            <WalletInfoPanel
              walletData={walletData}
              onAddToWatchList={onAddToWatchList}
            />
          </div>
        )}

        {/* Risk Analysis Tab */}
        {(activeTab === 'analysis' || !hasWalletData) && hasAnalysisData && (
          <div className="h-full">
            <WalletAnalysisPanel
              selectedWallet={selectedWalletForAnalysis}
              onClose={onCloseAnalysis}
              embedded={true}
              onAddToWatchList={onAddToWatchList}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default RightSidePanel;
