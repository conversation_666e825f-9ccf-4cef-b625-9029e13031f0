import { useState, useEffect } from 'react';
import Head from 'next/head';
import BubbleMapContainer from '@/components/BubbleMapContainer';
import SearchBar from '@/components/SearchBar';
import RightSidePanel from '@/components/RightSidePanel';
import Header from '@/components/Header';
import { FaChevronRight, FaChevronLeft, FaExpand, FaCompress } from 'react-icons/fa';

export default function BubbleMap() {
  const [searchWallet, setSearchWallet] = useState<string>('');
  const [selectedWallet, setSelectedWallet] = useState<any>(null);
  const [selectedWalletForAnalysis, setSelectedWalletForAnalysis] = useState<any>(null);
  const [showWalletAnalysis, setShowWalletAnalysis] = useState<boolean>(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [viewportHeight, setViewportHeight] = useState<number>(0);
  const [globalError, setGlobalError] = useState<string | null>(null);

  // Dynamic viewport height calculation
  useEffect(() => {
    const updateViewportHeight = () => {
      setViewportHeight(window.innerHeight);
    };

    updateViewportHeight();
    window.addEventListener('resize', updateViewportHeight);
    return () => window.removeEventListener('resize', updateViewportHeight);
  }, []);

  const handleSearch = (address: string) => {
    setSearchWallet(address);
  };

  const handleSelectWallet = (wallet: any) => {
    console.log("Wallet selected in BubbleMap component:", wallet);
    // Make sure we have a complete wallet object with all required properties
    const completeWallet = {
      id: wallet.id || '',
      address: wallet.address || '',
      label: wallet.label,
      balance: wallet.balance,
      transactionCount: wallet.transactionCount,
      tags: wallet.tags || [],
      // Social media connectivity
      socialProfiles: wallet.socialProfiles,
      hasVerifiedSocials: wallet.hasVerifiedSocials,
      socialScore: wallet.socialScore,
      // Add any other properties needed for display
    };

    setSelectedWallet(completeWallet);
    setSelectedWalletForAnalysis(wallet); // Set for analysis panel
    setShowWalletAnalysis(true); // Show analysis panel
    setSidebarCollapsed(false); // Open sidebar when a wallet is selected
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setSidebarCollapsed(true);
    }
  };

  // Handle adding wallet to watch list
  const handleAddToWatchList = (address: string) => {
    console.log('Wallet added to watch list:', address);
    // You can add additional logic here like showing a success message
  };

  // Handle focusing on a wallet from watch list
  const handleFocusWallet = (address: string) => {
    console.log('Focusing on wallet:', address);
    // Set the search wallet to trigger a search and focus
    setSearchWallet(address);
  };

  // Calculate dynamic heights
  const headerHeight = 80; // Approximate header height
  const searchHeight = 80; // Search bar height with padding
  const availableHeight = viewportHeight - headerHeight - (isFullscreen ? 0 : searchHeight);

  return (
    <>
      <Head>
        <title>Bubble Map - Crypto Bubble Map</title>
        <meta name="description" content="Interactive bubble map visualization of cryptocurrency wallet relationships and transaction networks" />
      </Head>
      
      <main className="h-screen overflow-hidden bg-background" style={{ maxWidth: '100vw', overflowX: 'hidden' }}>
        {/* Integrated Header with Search Bar */}
        <div className="absolute top-0 left-0 right-0 z-50 border-b bg-background/80 backdrop-blur-lg border-border/50">
          <Header onSearch={handleSearch} showSearch={!isFullscreen} />
        </div>

        {/* Global Error Notification - Always visible below header */}
        {globalError && (
          <div className="absolute z-[60] max-w-md mx-4 transform -translate-x-1/2 top-20 left-1/2">
            <div className="px-6 py-4 border glass-card rounded-xl shadow-bubble border-red-500/30 animate-slide-down">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium leading-relaxed text-red-400">{globalError}</span>
                <button
                  onClick={() => setGlobalError(null)}
                  className="ml-auto text-red-400 transition-colors hover:text-red-300"
                  aria-label="Dismiss error"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edge-to-Edge Bubble Map Container */}
        <div className="absolute inset-0">
          {/* Fullscreen controls overlay */}
          {isFullscreen && (
            <div className="absolute z-30 flex gap-3 top-6 left-6">
              <div className="p-3 glass-card rounded-xl">
                <SearchBar onSearch={handleSearch} compact={true} />
              </div>
              <button
                onClick={toggleFullscreen}
                className="p-3 transition-all duration-300 border glass-card hover:bg-accent-500 hover:text-white rounded-xl shadow-glow border-border-accent group"
                title="Exit Fullscreen"
              >
                <FaCompress size={16} className="transition-transform group-hover:scale-110" />
              </button>
            </div>
          )}

          {/* Full Viewport Bubble Map - Respects Header Space */}
          <div className={`absolute top-20 left-0 bottom-0 transition-all duration-500 ease-in-out ${
            isFullscreen || sidebarCollapsed
              ? 'right-0'
              : 'right-0' // Always use right-0 to prevent overflow, sidebar will overlay
          }`} style={{ maxWidth: '100vw', overflowX: 'hidden' }}>
            <BubbleMapContainer
              searchWallet={searchWallet}
              onSelectWallet={handleSelectWallet}
              onError={setGlobalError}
              onFocusWallet={handleFocusWallet}
            />
          </div>

        {/* Floating Sidebar - Right Edge with proper header spacing */}
        {!isFullscreen && (
          <div className={`
            absolute top-20 h-[calc(100vh-5rem)] z-30
            ${sidebarCollapsed
              ? 'w-0 opacity-0 invisible -right-96'
              : 'w-80 lg:w-96 opacity-100 visible right-0'
            }
            transition-all duration-500 ease-in-out
          `} style={{ maxWidth: 'calc(100vw - 16px)' }}>
            {/* Enhanced toggle button */}
            <button
              className="absolute z-30 flex items-center justify-center p-3 transition-all duration-300 transform -translate-y-1/2 border rounded-full -left-4 top-1/2 glass-card hover:bg-accent-500 hover:text-white shadow-bubble border-border-accent group"
              onClick={toggleSidebar}
              aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <div className="relative">
                {sidebarCollapsed ? (
                  <FaChevronLeft size={14} className="transition-transform group-hover:scale-110" />
                ) : (
                  <FaChevronRight size={14} className="transition-transform group-hover:scale-110" />
                )}
              </div>
            </button>

            {/* Mobile backdrop */}
            {!sidebarCollapsed && (
              <div
                className="absolute inset-0 lg:hidden bg-black/50 backdrop-blur-sm -z-10"
                onClick={toggleSidebar}
              />
            )}

            <div className="h-full border-l bg-background/95 backdrop-blur-lg border-border-secondary">
              <RightSidePanel
                walletData={selectedWallet}
                selectedWalletForAnalysis={selectedWalletForAnalysis}
                showWalletAnalysis={showWalletAnalysis}
                onCloseAnalysis={() => {
                  setShowWalletAnalysis(false);
                  setSelectedWalletForAnalysis(null);
                }}
                onClose={() => {
                  setSidebarCollapsed(true);
                  setSelectedWallet(null);
                  setSelectedWalletForAnalysis(null);
                  setShowWalletAnalysis(false);
                }}
                onAddToWatchList={handleAddToWatchList}
              />
            </div>
          </div>
        )}


        </div>
      </main>
    </>
  );
}
